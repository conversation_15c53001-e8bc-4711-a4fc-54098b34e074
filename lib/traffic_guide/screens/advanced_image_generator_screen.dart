import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';

import '../models/image_template.dart';
import '../models/traffic_guide_models.dart';
import '../services/advanced_image_service.dart';
import '../widgets/template_selector.dart';
import '../widgets/advanced_color_picker.dart';
import '../widgets/real_time_preview.dart';
import '../widgets/export_options.dart';

class AdvancedImageGeneratorScreen extends StatefulWidget {
  const AdvancedImageGeneratorScreen({super.key});

  @override
  State<AdvancedImageGeneratorScreen> createState() =>
      _AdvancedImageGeneratorScreenState();
}

class _AdvancedImageGeneratorScreenState
    extends State<AdvancedImageGeneratorScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  final AdvancedImageService _imageService = AdvancedImageService();
  final TextEditingController _textController = TextEditingController();

  // 状态变量
  ImageTemplate? _selectedTemplate;
  TrafficImageConfig _config = TrafficImageConfig(text: '');
  bool _isGenerating = false;

  @override
  void initState() {
    super.initState();

    _tabController = TabController(length: 3, vsync: this);

    // 设置默认模板
    _selectedTemplate = TemplatePresets.templates.first;
    _updateConfigFromTemplate();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _textController.dispose();
    super.dispose();
  }

  void _updateConfigFromTemplate() {
    if (_selectedTemplate != null) {
      setState(() {
        _config = _config.copyWith(
          fontSize: _selectedTemplate!.baseFontSize,
          fontFamily: _selectedTemplate!.recommendedFonts.first,
          backgroundColor:
              '#${_selectedTemplate!.primaryColors.first.toARGB32().toRadixString(16).substring(2)}',
          textColor:
              '#${_selectedTemplate!.secondaryColors.first.toARGB32().toRadixString(16).substring(2)}',
          noiseLevel: _selectedTemplate!.noiseLevel,
          distortionLevel: _selectedTemplate!.distortionLevel,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: SafeArea(
        child: Column(
          children: [_buildAppBar(), Expanded(child: _buildMobileLayout())],
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      children: [
        // 预览区域 - 放在顶部
        Container(
          height: 280,
          margin: const EdgeInsets.all(16),
          child: _buildPreviewPanel(),
        ),

        // 配置面板 - 放在底部，可滚动
        Expanded(
          child: Container(
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: _buildConfigPanel(),
          ),
        ),
      ],
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back_ios),
            style: IconButton.styleFrom(
              backgroundColor: Colors.grey.withValues(alpha: 0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  '引流图片生成器',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                Text(
                  '专业级图片生成',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          _buildMobileActionButtons(),
        ],
      ),
    );
  }

  Widget _buildMobileActionButtons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: _resetConfig,
          icon: const Icon(Icons.refresh),
          style: IconButton.styleFrom(
            backgroundColor: Colors.grey.withValues(alpha: 0.1),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          tooltip: '重置',
        ),
        const SizedBox(width: 8),
        ElevatedButton(
          onPressed:
              _config.text.isNotEmpty && !_isGenerating
                  ? _showExportOptions
                  : null,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            backgroundColor: const Color(0xFF667EEA),
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            minimumSize: const Size(80, 36),
          ),
          child:
              _isGenerating
                  ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                  : const Text('导出', style: TextStyle(fontSize: 14)),
        ),
      ],
    );
  }

  Widget _buildConfigPanel() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          // 标签栏
          Container(
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
            ),
            child: TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: '模板', icon: Icon(Icons.palette, size: 18)),
                Tab(text: '文字', icon: Icon(Icons.text_fields, size: 18)),
                Tab(text: '效果', icon: Icon(Icons.auto_fix_high, size: 18)),
              ],
              labelColor: const Color(0xFF667EEA),
              unselectedLabelColor: Colors.grey[600],
              indicatorColor: const Color(0xFF667EEA),
              indicatorWeight: 3,
              labelStyle: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: const TextStyle(fontSize: 12),
            ),
          ),

          // 内容区域
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildTemplateTab(),
                _buildTextTab(),
                _buildEffectsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: TemplateSelector(
        templates: TemplatePresets.templates,
        selectedTemplate: _selectedTemplate,
        onTemplateSelected: (template) {
          setState(() {
            _selectedTemplate = template;
            _updateConfigFromTemplate();
          });
        },
      ),
    );
  }

  Widget _buildTextTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '文字内容',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // 文字输入框
            Container(
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
              ),
              child: TextField(
                controller: _textController,
                maxLines: 4,
                decoration: const InputDecoration(
                  hintText: '输入要生成的文字内容...',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.all(16),
                ),
                style: const TextStyle(fontSize: 16),
                onChanged: (value) {
                  setState(() {
                    _config = _config.copyWith(text: value);
                  });
                },
              ),
            ),

            const SizedBox(height: 24),

            // 字体设置
            _buildFontSettings(),

            const SizedBox(height: 24),

            // 颜色设置
            _buildColorSettings(),
          ],
        ),
      ),
    );
  }

  Widget _buildEffectsTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '视觉效果',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            _buildEffectSlider(
              '干扰程度',
              _config.noiseLevel,
              (value) =>
                  setState(() => _config = _config.copyWith(noiseLevel: value)),
            ),

            _buildEffectSlider(
              '扭曲程度',
              _config.distortionLevel,
              (value) => setState(
                () => _config = _config.copyWith(distortionLevel: value),
              ),
            ),

            const SizedBox(height: 24),

            // 水印设置
            _buildWatermarkSettings(),
          ],
        ),
      ),
    );
  }

  Widget _buildFontSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '字体设置',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('字体大小'),
                  const SizedBox(height: 8),
                  Slider(
                    value: _config.fontSize,
                    min: 20,
                    max: 80,
                    divisions: 12,
                    label: _config.fontSize.toInt().toString(),
                    onChanged: (value) {
                      setState(() {
                        _config = _config.copyWith(fontSize: value);
                      });
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildColorSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '颜色设置',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () => _showColorPickerDialog(true),
                child: Container(
                  height: 50,
                  decoration: BoxDecoration(
                    color: _parseColor(_config.textColor),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.3),
                    ),
                  ),
                  child: const Center(
                    child: Text(
                      '文字颜色',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: GestureDetector(
                onTap: () => _showColorPickerDialog(false),
                child: Container(
                  height: 50,
                  decoration: BoxDecoration(
                    color: _parseColor(_config.backgroundColor),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.3),
                    ),
                  ),
                  child: const Center(
                    child: Text(
                      '背景颜色',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEffectSlider(
    String label,
    double value,
    Function(double) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
            Text(
              '${(value * 100).toInt()}%',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: const Color(0xFF667EEA),
            thumbColor: const Color(0xFF667EEA),
            overlayColor: const Color(0xFF667EEA).withValues(alpha: 0.2),
          ),
          child: Slider(value: value, onChanged: onChanged),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildWatermarkSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Checkbox(
              value: _config.addWatermark,
              onChanged: (value) {
                setState(() {
                  _config = _config.copyWith(addWatermark: value ?? false);
                });
              },
            ),
            const Text('添加水印'),
          ],
        ),

        if (_config.addWatermark) ...[
          const SizedBox(height: 12),
          TextField(
            decoration: const InputDecoration(
              labelText: '水印文本',
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _config = _config.copyWith(watermarkText: value);
              });
            },
          ),
        ],
      ],
    );
  }

  Widget _buildPreviewPanel() {
    return RealTimePreview(
      config: _config,
      template: _selectedTemplate,
      isGenerating: _isGenerating,
    );
  }

  void _showColorPickerDialog(bool isTextColor) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: SizedBox(
              width: 400,
              child: AdvancedColorPicker(
                initialColor: _parseColor(
                  isTextColor ? _config.textColor : _config.backgroundColor,
                ),
                onColorChanged: (color) {
                  setState(() {
                    if (isTextColor) {
                      _config = _config.copyWith(
                        textColor:
                            '#${color.toARGB32().toRadixString(16).substring(2)}',
                      );
                    } else {
                      _config = _config.copyWith(
                        backgroundColor:
                            '#${color.toARGB32().toRadixString(16).substring(2)}',
                      );
                    }
                  });
                },
                presetColors: _selectedTemplate?.primaryColors ?? [],
              ),
            ),
          ),
    );
  }

  void _showExportOptions() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => SizedBox(
            height: MediaQuery.of(context).size.height * 0.8,
            child: ExportOptions(
              onExport: _handleExport,
              isExporting: _isGenerating,
            ),
          ),
    );
  }

  void _handleExport(ExportConfig exportConfig) async {
    if (_selectedTemplate == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请先选择一个模板')));
      return;
    }

    setState(() => _isGenerating = true);

    try {
      // 1. 生成图片
      final imageBytes = await _imageService.generateImageWithTemplate(
        _config,
        _selectedTemplate!,
        exportConfig: exportConfig,
      );

      // 2. 请求权限并保存到相册
      await _saveToGallery(imageBytes);

      setState(() {
        _isGenerating = false;
      });

      Navigator.pop(context); // 关闭导出选项

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('✅ 图片已成功保存到相册'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      setState(() => _isGenerating = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('保存失败: $e'), backgroundColor: Colors.red),
      );
    }
  }

  Future<void> _saveToGallery(Uint8List imageBytes) async {
    // 请求权限
    bool hasPermission = false;

    if (Theme.of(context).platform == TargetPlatform.iOS) {
      // iOS 使用 photos 权限
      final permission = await Permission.photos.request();
      hasPermission = permission.isGranted;

      if (!hasPermission && permission.isPermanentlyDenied) {
        throw Exception('相册权限被永久拒绝，请到设置中手动开启权限');
      }
    } else {
      // Android 尝试多种权限
      var photosPermission = await Permission.photos.request();
      if (photosPermission.isGranted) {
        hasPermission = true;
      } else {
        var storagePermission = await Permission.storage.request();
        hasPermission = storagePermission.isGranted;
      }
    }

    if (!hasPermission) {
      throw Exception('需要相册权限才能保存图片，请在设置中开启权限');
    }

    // 保存到相册
    final result = await ImageGallerySaver.saveImage(
      imageBytes,
      name: 'ContentPal_TrafficImage_${DateTime.now().millisecondsSinceEpoch}',
      quality: 100,
      isReturnImagePathOfIOS: true, // iOS返回图片路径
    );

    // 检查保存结果
    if (result != null) {
      bool isSuccess = false;
      if (result is Map) {
        isSuccess = result['isSuccess'] == true;
      } else if (result is String && result.isNotEmpty) {
        // iOS可能直接返回路径字符串
        isSuccess = true;
      }

      if (!isSuccess) {
        final errorMsg =
            result is Map ? (result['errorMessage'] ?? '未知错误') : '保存失败';
        throw Exception('保存失败：$errorMsg');
      }
    } else {
      throw Exception('保存失败：返回结果为空');
    }
  }

  void _resetConfig() {
    setState(() {
      _config = TrafficImageConfig(text: '');
      _textController.clear();
      _selectedTemplate = TemplatePresets.templates.first;
      _updateConfigFromTemplate();
    });
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      final hex = colorString.substring(1);
      return Color(int.parse('FF$hex', radix: 16));
    }
    return Colors.black;
  }
}
