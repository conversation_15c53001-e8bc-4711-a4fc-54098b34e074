import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'models/traffic_guide_models.dart';
import 'services/traffic_guide_service.dart';
import 'screens/image_generator_screen.dart';
import 'screens/advanced_image_generator_screen.dart';
import 'screens/text_transformer_screen.dart';
import 'screens/watermark_screen.dart';
import 'screens/project_editor_screen.dart';

class TrafficGuideHomePage extends StatefulWidget {
  const TrafficGuideHomePage({super.key});

  @override
  State<TrafficGuideHomePage> createState() => _TrafficGuideHomePageState();
}

class _TrafficGuideHomePageState extends State<TrafficGuideHomePage> {
  final TrafficGuideService _service = TrafficGuideService();
  List<TrafficGuideProject> _projects = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  Future<void> _loadProjects() async {
    setState(() => _isLoading = true);
    final projects = await _service.getAllProjects();
    setState(() {
      _projects = projects;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('内容引流工具'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  // 功能卡片区域
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '功能工具',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        GridView.count(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          crossAxisCount: 2,
                          crossAxisSpacing: 16,
                          mainAxisSpacing: 16,
                          childAspectRatio: 1.2,
                          children: [
                            _buildFunctionCard(
                              icon: Icons.auto_awesome,
                              title: '高级图片生成器',
                              subtitle: '全球顶尖水平的引流图片',
                              color: const Color(0xFF667EEA),
                              onTap: () => _navigateToAdvancedImageGenerator(),
                              isNew: true,
                            ),
                            _buildFunctionCard(
                              icon: Icons.text_fields,
                              title: '文本转换',
                              subtitle: 'Emoji转换和字符干扰',
                              color: Colors.green,
                              onTap: () => _navigateToTextTransformer(),
                            ),
                            _buildFunctionCard(
                              icon: Icons.water_drop,
                              title: '水印处理',
                              subtitle: '添加和移除隐形水印',
                              color: Colors.orange,
                              onTap: () => _navigateToWatermark(),
                            ),
                            _buildFunctionCard(
                              icon: Icons.add_circle,
                              title: '新建项目',
                              subtitle: '创建引流项目配置',
                              color: Colors.purple,
                              onTap: () => _createNewProject(),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // 项目列表区域
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                '我的项目',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              if (_projects.isNotEmpty)
                                TextButton(
                                  onPressed: _loadProjects,
                                  child: const Text('刷新'),
                                ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Expanded(
                            child:
                                _projects.isEmpty
                                    ? _buildEmptyState()
                                    : _buildProjectList(),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
    );
  }

  Widget _buildFunctionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
    bool isNew = false,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Stack(
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(icon, size: 40, color: color),
                  const SizedBox(height: 12),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
              if (isNew)
                Positioned(
                  top: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      'NEW',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.folder_open, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text('暂无项目', style: TextStyle(fontSize: 18, color: Colors.grey[600])),
          const SizedBox(height: 8),
          Text(
            '点击"新建项目"开始创建',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildProjectList() {
    return ListView.builder(
      itemCount: _projects.length,
      itemBuilder: (context, index) {
        final project = _projects[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.blue.withValues(alpha: 0.1),
              child: Icon(Icons.folder, color: Colors.blue),
            ),
            title: Text(
              project.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(project.description),
                const SizedBox(height: 4),
                Text(
                  '更新时间: ${_formatDate(project.updatedAt)}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (value) => _handleProjectAction(value, project),
              itemBuilder:
                  (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('编辑'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('删除', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
            ),
            onTap: () => _editProject(project),
          ),
        );
      },
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  void _navigateToImageGenerator() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ImageGeneratorScreen()),
    );
  }

  void _navigateToAdvancedImageGenerator() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AdvancedImageGeneratorScreen(),
      ),
    );
  }

  void _navigateToTextTransformer() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const TextTransformerScreen()),
    );
  }

  void _navigateToWatermark() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const WatermarkScreen()),
    );
  }

  void _createNewProject() {
    final uuid = const Uuid();
    final newProject = TrafficGuideProject(
      id: uuid.v4(),
      name: '新项目',
      description: '引流项目配置',
      imageConfig: TrafficImageConfig(text: ''),
      textConfig: TextTransformConfig(),
      watermarkConfig: WatermarkConfig(text: ''),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProjectEditorScreen(project: newProject),
      ),
    ).then((_) => _loadProjects());
  }

  void _editProject(TrafficGuideProject project) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProjectEditorScreen(project: project),
      ),
    ).then((_) => _loadProjects());
  }

  void _handleProjectAction(String action, TrafficGuideProject project) {
    switch (action) {
      case 'edit':
        _editProject(project);
        break;
      case 'delete':
        _deleteProject(project);
        break;
    }
  }

  Future<void> _deleteProject(TrafficGuideProject project) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('确认删除'),
            content: Text('确定要删除项目"${project.name}"吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('删除'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      // 这里应该调用服务删除项目
      setState(() {
        _projects.removeWhere((p) => p.id == project.id);
      });
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('项目"${project.name}"已删除')));
    }
  }
}
