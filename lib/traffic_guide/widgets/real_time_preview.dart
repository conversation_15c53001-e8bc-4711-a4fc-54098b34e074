import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import '../models/image_template.dart';
import '../models/traffic_guide_models.dart';
import '../utils/image_dimension_calculator.dart';

class RealTimePreview extends StatefulWidget {
  final TrafficImageConfig config;
  final ImageTemplate? template;
  final Function(Uint8List)? onImageGenerated;
  final bool isGenerating;

  const RealTimePreview({
    super.key,
    required this.config,
    this.template,
    this.onImageGenerated,
    this.isGenerating = false,
  });

  @override
  State<RealTimePreview> createState() => _RealTimePreviewState();
}

class _RealTimePreviewState extends State<RealTimePreview>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _shimmerController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _shimmerAnimation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _shimmerController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _shimmerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.visibility,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    '实时预览',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
                if (widget.isGenerating)
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // 预览区域
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _buildPreviewContent(),
            ),
          ),

          // 操作按钮
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildPreviewContent() {
    if (widget.config.text.isEmpty) {
      return _buildEmptyState();
    }

    // 使用新的尺寸计算工具获取最优宽高比
    final aspectRatio = ImageDimensionCalculator.getOptimalAspectRatio(
      widget.config,
    );

    return Center(
      child: AspectRatio(
        aspectRatio: aspectRatio,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.grey.withValues(alpha: 0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: LayoutBuilder(
              builder: (context, constraints) {
                final previewSize = Size(
                  constraints.maxWidth,
                  constraints.maxHeight,
                );
                return Stack(
                  children: [
                    // 背景
                    _buildBackground(),

                    // 文字内容 - 使用响应式渲染
                    _buildResponsiveTextContent(previewSize),

                    // 加载遮罩
                    if (widget.isGenerating) _buildLoadingOverlay(),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBackground() {
    final template = widget.template;
    final backgroundColor = _parseColor(widget.config.backgroundColor);

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [backgroundColor, backgroundColor.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Stack(
        children: [
          // 模板背景效果（如果有模板）
          if (template != null) _buildBackgroundEffects(template),

          // 扭曲效果
          if (widget.config.distortionLevel > 0) _buildDistortionEffect(),
        ],
      ),
    );
  }

  Widget _buildBackgroundEffects(ImageTemplate template) {
    return Stack(
      children: [
        // 纹理效果
        if (template.backgroundType == BackgroundType.texture)
          _buildTextureEffect(),

        // 几何图案
        if (template.backgroundType == BackgroundType.geometric)
          _buildGeometricPattern(),

        // 装饰元素
        ...template.decorations.map(
          (decoration) => _buildDecorationElement(decoration),
        ),
      ],
    );
  }

  Widget _buildTextureEffect() {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment(_shimmerAnimation.value - 1, 0),
              end: Alignment(_shimmerAnimation.value, 0),
              colors: [
                Colors.transparent,
                Colors.white.withValues(alpha: 0.1),
                Colors.transparent,
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildGeometricPattern() {
    return CustomPaint(painter: GeometricPatternPainter(), size: Size.infinite);
  }

  Widget _buildDecorationElement(DecorationElement decoration) {
    return Positioned(
      left: decoration.position.dx,
      top: decoration.position.dy,
      child: Transform.rotate(
        angle: decoration.rotation,
        child: Container(
          width: decoration.size.width,
          height: decoration.size.height,
          decoration: BoxDecoration(
            color: decoration.color.withValues(alpha: decoration.opacity),
            shape:
                decoration.type == 'circle'
                    ? BoxShape.circle
                    : BoxShape.rectangle,
            borderRadius:
                decoration.type == 'rectangle'
                    ? BorderRadius.circular(4)
                    : null,
          ),
        ),
      ),
    );
  }

  Widget _buildResponsiveTextContent(Size previewSize) {
    final template = widget.template;
    final optimalSize = ImageDimensionCalculator.calculateOptimalSize(
      widget.config,
    );

    // 计算响应式字体大小和内边距
    final responsiveFontSize =
        ImageDimensionCalculator.calculatePreviewFontSize(
          widget.config,
          previewSize,
          template: template,
        );

    final responsivePadding = ImageDimensionCalculator.calculatePreviewPadding(
      previewSize,
      optimalSize,
    );

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.isGenerating ? _pulseAnimation.value : 1.0,
          child: Stack(
            children: [
              // 背景效果
              if (template != null) _buildBackgroundEffects(template),

              // 文字内容
              Positioned.fill(
                child: Padding(
                  padding: EdgeInsets.all(responsivePadding),
                  child: Center(
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        maxWidth: previewSize.width * 0.9, // 与导出服务保持一致的90%宽度约束
                      ),
                      child: Text(
                        widget.config.text,
                        style: TextStyle(
                          fontSize: responsiveFontSize,
                          fontWeight: template?.fontWeight ?? FontWeight.bold,
                          color: _parseColor(widget.config.textColor),
                          fontFamily: widget.config.fontFamily,
                          shadows: [
                            Shadow(
                              offset: const Offset(1, 1),
                              blurRadius: 2,
                              color: Colors.black.withValues(alpha: 0.3),
                            ),
                          ],
                        ),
                        textAlign: _getTextAlign(template?.textLayout),
                        maxLines: null,
                        softWrap: true,
                        overflow: TextOverflow.visible, // 确保文本不会被截断
                      ),
                    ),
                  ),
                ),
              ),

              // 干扰效果
              if (widget.config.noiseLevel > 0) _buildNoiseEffect(),

              // 水印
              if (widget.config.addWatermark &&
                  widget.config.watermarkText.isNotEmpty)
                _buildWatermarkOverlay(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDistortionEffect() {
    return Positioned.fill(
      child: CustomPaint(
        painter: DistortionPainter(widget.config.distortionLevel),
      ),
    );
  }

  Widget _buildNoiseEffect() {
    return Positioned.fill(
      child: CustomPaint(painter: NoisePainter(widget.config.noiseLevel)),
    );
  }

  Widget _buildWatermarkOverlay() {
    return Positioned(
      bottom: 8,
      right: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          widget.config.watermarkText,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 10,
            fontWeight: FontWeight.w300,
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment(_shimmerAnimation.value - 1, -1),
              end: Alignment(_shimmerAnimation.value, 1),
              colors: [
                Colors.transparent,
                Colors.white.withValues(alpha: 0.3),
                Colors.transparent,
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.image_outlined, size: 48, color: Colors.grey[400]),
          const SizedBox(height: 12),
          Text(
            '输入文字开始预览',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
          const SizedBox(height: 6),
          Text(
            '选择模板和配置参数\n实时查看效果',
            style: TextStyle(fontSize: 12, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.05),
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(16)),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: widget.config.text.isNotEmpty ? _refreshPreview : null,
              icon: const Icon(Icons.refresh, size: 16),
              label: const Text('刷新', style: TextStyle(fontSize: 12)),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 8),
                minimumSize: const Size(0, 32),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: ElevatedButton.icon(
              onPressed:
                  widget.config.text.isNotEmpty && !widget.isGenerating
                      ? _generateImage
                      : null,
              icon:
                  widget.isGenerating
                      ? const SizedBox(
                        width: 12,
                        height: 12,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Icon(Icons.download, size: 16),
              label: Text(
                widget.isGenerating ? '生成中...' : '生成',
                style: const TextStyle(fontSize: 12),
              ),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 8),
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                minimumSize: const Size(0, 32),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _refreshPreview() {
    setState(() {
      // 触发重新构建以刷新预览
    });
  }

  void _generateImage() {
    if (widget.onImageGenerated != null) {
      // 这里应该调用图片生成服务
      // widget.onImageGenerated!(generatedImageBytes);
    }
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      final hex = colorString.substring(1);
      return Color(int.parse('FF$hex', radix: 16));
    }
    return Colors.black;
  }

  TextAlign _getTextAlign(TextLayout? layout) {
    switch (layout) {
      case TextLayout.left:
        return TextAlign.left;
      case TextLayout.right:
        return TextAlign.right;
      case TextLayout.center:
      default:
        return TextAlign.center;
    }
  }
}

class GeometricPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.1)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1;

    // 绘制几何图案
    for (int i = 0; i < 5; i++) {
      final radius = (i + 1) * 20.0;
      canvas.drawCircle(
        Offset(size.width * 0.2, size.height * 0.3),
        radius,
        paint,
      );
    }

    for (int i = 0; i < 3; i++) {
      final size1 = (i + 1) * 15.0;
      canvas.drawRect(
        Rect.fromCenter(
          center: Offset(size.width * 0.8, size.height * 0.7),
          width: size1,
          height: size1,
        ),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class NoisePainter extends CustomPainter {
  final double noiseLevel;

  NoisePainter(this.noiseLevel);

  @override
  void paint(Canvas canvas, Size size) {
    if (noiseLevel <= 0) return;

    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.1 * noiseLevel)
          ..style = PaintingStyle.fill;

    final random = math.Random(42); // 固定种子，确保一致性
    final pointCount = (size.width * size.height * noiseLevel * 0.001).toInt();

    for (int i = 0; i < pointCount; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = random.nextDouble() * 2 + 1;

      canvas.drawCircle(Offset(x, y), radius, paint);
    }

    // 添加一些线条干扰
    final linePaint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.05 * noiseLevel)
          ..strokeWidth = 1;

    for (int i = 0; i < (10 * noiseLevel).toInt(); i++) {
      final startX = random.nextDouble() * size.width;
      final startY = random.nextDouble() * size.height;
      final endX = startX + (random.nextDouble() - 0.5) * 50;
      final endY = startY + (random.nextDouble() - 0.5) * 50;

      canvas.drawLine(Offset(startX, startY), Offset(endX, endY), linePaint);
    }
  }

  @override
  bool shouldRepaint(NoisePainter oldDelegate) {
    return oldDelegate.noiseLevel != noiseLevel;
  }
}

class DistortionPainter extends CustomPainter {
  final double distortionLevel;

  DistortionPainter(this.distortionLevel);

  @override
  void paint(Canvas canvas, Size size) {
    if (distortionLevel <= 0) return;

    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.05 * distortionLevel)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1;

    final random = math.Random(123); // 固定种子

    // 绘制扭曲线条
    for (int i = 0; i < (20 * distortionLevel).toInt(); i++) {
      final path = Path();
      final startX = random.nextDouble() * size.width;
      final startY = random.nextDouble() * size.height;

      path.moveTo(startX, startY);

      // 创建波浪形路径
      for (int j = 1; j <= 5; j++) {
        final x =
            startX +
            j * 20 +
            (random.nextDouble() - 0.5) * 30 * distortionLevel;
        final y = startY + (random.nextDouble() - 0.5) * 40 * distortionLevel;
        path.lineTo(x, y);
      }

      canvas.drawPath(path, paint);
    }

    // 添加一些几何扭曲
    final shapePaint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.03 * distortionLevel)
          ..style = PaintingStyle.fill;

    for (int i = 0; i < (5 * distortionLevel).toInt(); i++) {
      final centerX = random.nextDouble() * size.width;
      final centerY = random.nextDouble() * size.height;
      final radius = 10 + random.nextDouble() * 20;

      canvas.drawCircle(Offset(centerX, centerY), radius, shapePaint);
    }
  }

  @override
  bool shouldRepaint(DistortionPainter oldDelegate) {
    return oldDelegate.distortionLevel != distortionLevel;
  }
}
