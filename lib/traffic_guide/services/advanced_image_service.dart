import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../models/image_template.dart';
import '../models/traffic_guide_models.dart';
import '../widgets/export_options.dart';

class AdvancedImageService {
  static final AdvancedImageService _instance =
      AdvancedImageService._internal();
  factory AdvancedImageService() => _instance;
  AdvancedImageService._internal();

  /// 使用模板生成高质量引流图片
  Future<Uint8List> generateImageWithTemplate(
    TrafficImageConfig config,
    ImageTemplate template, {
    ExportConfig? exportConfig,
  }) async {
    final size = _calculateOptimalSize(config, exportConfig);
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);

    // 1. 绘制背景
    _drawBackground(canvas, size, config, template);

    // 2. 绘制文字内容
    await _drawText(canvas, size, config, template);

    // 3. 应用视觉效果
    _applyEffects(canvas, size, config);

    // 4. 添加水印（如果需要）
    if (config.addWatermark && config.watermarkText.isNotEmpty) {
      _addWatermark(canvas, size, config);
    }

    final picture = recorder.endRecording();
    final image = await picture.toImage(
      size.width.toInt(),
      size.height.toInt(),
    );

    final format =
        exportConfig?.format == ExportFormat.jpg
            ? ui.ImageByteFormat.rawRgba
            : ui.ImageByteFormat.png;

    final byteData = await image.toByteData(format: format);
    return byteData!.buffer.asUint8List();
  }

  /// 计算最优图片尺寸
  Size _calculateOptimalSize(
    TrafficImageConfig config,
    ExportConfig? exportConfig,
  ) {
    // 如果有导出配置，优先使用
    final configSize = exportConfig?.size;
    if (configSize != null) {
      return configSize;
    }

    // 根据文字内容计算合适的尺寸
    final textLength = config.text.length;
    final fontSize = config.fontSize;

    // 基础尺寸
    double baseWidth = 800;
    double baseHeight = 600;

    // 根据文字长度调整
    if (textLength > 50) {
      baseWidth = 900;
      baseHeight = 600;
    }
    if (textLength > 100) {
      baseWidth = 1000;
      baseHeight = 600;
    }
    if (textLength > 200) {
      baseWidth = 1200;
      baseHeight = 600;
    }

    // 根据字体大小调整
    final fontSizeMultiplier = fontSize / 48.0;
    baseWidth *= fontSizeMultiplier;
    baseHeight *= fontSizeMultiplier;

    // 限制最大尺寸，避免图片过大
    baseWidth = baseWidth.clamp(600.0, 1500.0);
    baseHeight = baseHeight.clamp(400.0, 1000.0);

    return Size(baseWidth, baseHeight);
  }

  /// 绘制背景
  void _drawBackground(
    Canvas canvas,
    Size size,
    TrafficImageConfig config,
    ImageTemplate template,
  ) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final backgroundColor = _parseColor(config.backgroundColor);

    final gradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [backgroundColor, backgroundColor.withValues(alpha: 0.8)],
    );

    canvas.drawRect(rect, Paint()..shader = gradient.createShader(rect));
  }

  /// 绘制文字
  Future<void> _drawText(
    Canvas canvas,
    Size size,
    TrafficImageConfig config,
    ImageTemplate template,
  ) async {
    if (config.text.isEmpty) return;

    final style = TextStyle(
      fontSize: config.fontSize,
      fontWeight: template.fontWeight,
      color: _parseColor(config.textColor),
      fontFamily: config.fontFamily,
      shadows: [
        Shadow(
          offset: const Offset(2, 2),
          blurRadius: 4,
          color: Colors.black.withValues(alpha: 0.3),
        ),
      ],
    );

    final textSpan = TextSpan(text: config.text, style: style);
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    textPainter.layout(maxWidth: size.width * 0.9);

    final offset = Offset(
      (size.width - textPainter.width) / 2,
      (size.height - textPainter.height) / 2,
    );

    textPainter.paint(canvas, offset);
  }

  /// 应用视觉效果
  void _applyEffects(Canvas canvas, Size size, TrafficImageConfig config) {
    // 添加干扰效果
    if (config.noiseLevel > 0) {
      _addNoise(canvas, size, config.noiseLevel);
    }

    // 添加扭曲效果
    if (config.distortionLevel > 0) {
      _addDistortion(canvas, size, config.distortionLevel);
    }
  }

  /// 添加水印
  void _addWatermark(Canvas canvas, Size size, TrafficImageConfig config) {
    final style = TextStyle(
      fontSize: 12,
      color: Colors.white.withValues(alpha: 0.6),
      fontWeight: FontWeight.w300,
    );

    final textSpan = TextSpan(text: config.watermarkText, style: style);
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    final offset = Offset(
      size.width - textPainter.width - 20,
      size.height - textPainter.height - 20,
    );

    textPainter.paint(canvas, offset);
  }

  /// 添加干扰效果
  void _addNoise(Canvas canvas, Size size, double level) {
    final random = math.Random(42);
    final paint = Paint()..color = Colors.white.withValues(alpha: 0.1 * level);

    for (int i = 0; i < (500 * level).toInt(); i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = random.nextDouble() * 2;
      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  /// 添加扭曲效果
  void _addDistortion(Canvas canvas, Size size, double level) {
    final random = math.Random(123);
    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.05 * level)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1;

    for (int i = 0; i < (20 * level).toInt(); i++) {
      final path = Path();
      final startX = random.nextDouble() * size.width;
      final startY = random.nextDouble() * size.height;

      path.moveTo(startX, startY);

      for (int j = 1; j <= 5; j++) {
        final x = startX + j * 20 + (random.nextDouble() - 0.5) * 30 * level;
        final y = startY + (random.nextDouble() - 0.5) * 40 * level;
        path.lineTo(x, y);
      }

      canvas.drawPath(path, paint);
    }
  }

  /// 解析颜色字符串
  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      final hex = colorString.substring(1);
      return Color(int.parse('FF$hex', radix: 16));
    }
    return Colors.black;
  }



  /// 居中文字布局
  Future<void> _drawCenteredText(
    Canvas canvas,
    Size size,
    TrafficImageConfig config,
    ImageTemplate template,
  ) async {
    final style = TextStyle(
      fontSize: template.baseFontSize,
      fontWeight: template.fontWeight,
      color: template.secondaryColors.first,
      fontFamily: template.recommendedFonts.first,
      shadows:
          template.hasShadow
              ? [
                Shadow(
                  offset: const Offset(3, 3),
                  blurRadius: 6,
                  color: Colors.black.withValues(alpha: 0.4),
                ),
              ]
              : null,
    );

    final textSpan = TextSpan(text: config.text, style: style);
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    textPainter.layout(maxWidth: size.width * 0.8);

    // 添加背景装饰
    if (template.hasGlow) {
      _addTextGlow(canvas, textPainter, size);
    }

    // 绘制文字
    final offset = Offset(
      (size.width - textPainter.width) / 2,
      (size.height - textPainter.height) / 2,
    );

    textPainter.paint(canvas, offset);

    // 添加文字装饰效果
    if (template.hasOutline) {
      _addTextOutline(canvas, textPainter, offset, template);
    }
  }

  /// 散布文字布局
  Future<void> _drawScatteredText(
    Canvas canvas,
    Size size,
    TrafficImageConfig config,
    ImageTemplate template,
  ) async {
    final characters = config.text.split('');
    final random = math.Random();

    for (int i = 0; i < characters.length; i++) {
      final char = characters[i];
      if (char.trim().isEmpty) continue;

      // 计算字符位置
      final x = size.width * 0.1 + random.nextDouble() * size.width * 0.8;
      final y = size.height * 0.2 + random.nextDouble() * size.height * 0.6;

      // 随机旋转和缩放
      final rotation = (random.nextDouble() - 0.5) * 0.6;
      final scale = 0.8 + random.nextDouble() * 0.4;

      canvas.save();
      canvas.translate(x, y);
      canvas.rotate(rotation);
      canvas.scale(scale);

      final style = TextStyle(
        fontSize: template.baseFontSize * (0.8 + random.nextDouble() * 0.4),
        fontWeight: template.fontWeight,
        color: template.secondaryColors.first.withValues(
          alpha: 0.7 + random.nextDouble() * 0.3,
        ),
        fontFamily: template.recommendedFonts.first,
      );

      final textSpan = TextSpan(text: char, style: style);
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(-textPainter.width / 2, -textPainter.height / 2),
      );

      canvas.restore();
    }
  }

  /// 曲线文字布局
  Future<void> _drawCurvedText(
    Canvas canvas,
    Size size,
    TrafficImageConfig config,
    ImageTemplate template,
  ) async {
    final characters = config.text.split('');
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    final radius = math.min(size.width, size.height) * 0.3;

    for (int i = 0; i < characters.length; i++) {
      final char = characters[i];
      if (char.trim().isEmpty) continue;

      final angle = (i / characters.length) * 2 * math.pi - math.pi / 2;
      final x = centerX + radius * math.cos(angle);
      final y = centerY + radius * math.sin(angle);

      canvas.save();
      canvas.translate(x, y);
      canvas.rotate(angle + math.pi / 2);

      final style = TextStyle(
        fontSize: template.baseFontSize,
        fontWeight: template.fontWeight,
        color: template.secondaryColors.first,
        fontFamily: template.recommendedFonts.first,
      );

      final textSpan = TextSpan(text: char, style: style);
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(-textPainter.width / 2, -textPainter.height / 2),
      );

      canvas.restore();
    }
  }

  /// 对角线文字布局
  Future<void> _drawDiagonalText(
    Canvas canvas,
    Size size,
    TrafficImageConfig config,
    ImageTemplate template,
  ) async {
    final style = TextStyle(
      fontSize: template.baseFontSize,
      fontWeight: template.fontWeight,
      color: template.secondaryColors.first,
      fontFamily: template.recommendedFonts.first,
    );

    final textSpan = TextSpan(text: config.text, style: style);
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    canvas.save();
    canvas.translate(size.width / 2, size.height / 2);
    canvas.rotate(-math.pi / 6); // 30度倾斜
    textPainter.paint(
      canvas,
      Offset(-textPainter.width / 2, -textPainter.height / 2),
    );
    canvas.restore();
  }



  void _drawGradientBackground(
    Canvas canvas,
    Rect rect,
    ImageTemplate template,
  ) {
    final gradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: template.primaryColors,
      stops: _generateGradientStops(template.primaryColors.length),
    );

    canvas.drawRect(rect, Paint()..shader = gradient.createShader(rect));
  }

  void _drawPatternBackground(
    Canvas canvas,
    Rect rect,
    ImageTemplate template,
  ) {
    // 先绘制基础渐变
    _drawGradientBackground(canvas, rect, template);

    // 添加图案
    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.1)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1;

    for (int i = 0; i < 20; i++) {
      for (int j = 0; j < 15; j++) {
        final x = i * 40.0;
        final y = j * 40.0;
        canvas.drawCircle(Offset(x, y), 15, paint);
      }
    }
  }

  void _drawTextureBackground(
    Canvas canvas,
    Rect rect,
    ImageTemplate template,
  ) {
    _drawGradientBackground(canvas, rect, template);

    final random = math.Random();
    final paint = Paint()..color = Colors.white.withValues(alpha: 0.05);

    // 添加纹理点
    for (int i = 0; i < 1000; i++) {
      final x = random.nextDouble() * rect.width;
      final y = random.nextDouble() * rect.height;
      final radius = random.nextDouble() * 2;
      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  void _drawGeometricBackground(
    Canvas canvas,
    Rect rect,
    ImageTemplate template,
  ) {
    _drawGradientBackground(canvas, rect, template);

    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.1)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2;

    // 绘制几何图形
    for (int i = 0; i < 5; i++) {
      final path = Path();
      final centerX = rect.width * (0.2 + i * 0.15);
      final centerY = rect.height * 0.5;
      final radius = 30.0 + i * 10;

      // 绘制多边形
      for (int j = 0; j <= 6; j++) {
        final angle = j * math.pi / 3;
        final x = centerX + radius * math.cos(angle);
        final y = centerY + radius * math.sin(angle);

        if (j == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }

      canvas.drawPath(path, paint);
    }
  }

  void _drawSolidBackground(Canvas canvas, Rect rect, ImageTemplate template) {
    canvas.drawRect(rect, Paint()..color = template.primaryColors.first);
  }



  void _drawDecorationElement(Canvas canvas, DecorationElement decoration) {
    final paint =
        Paint()..color = decoration.color.withValues(alpha: decoration.opacity);

    canvas.save();
    canvas.translate(decoration.position.dx, decoration.position.dy);
    canvas.rotate(decoration.rotation);

    switch (decoration.type) {
      case 'circle':
        canvas.drawCircle(Offset.zero, decoration.size.width / 2, paint);
        break;
      case 'rectangle':
        canvas.drawRect(
          Rect.fromCenter(
            center: Offset.zero,
            width: decoration.size.width,
            height: decoration.size.height,
          ),
          paint,
        );
        break;
      case 'line':
        paint.style = PaintingStyle.stroke;
        paint.strokeWidth = 2;
        canvas.drawLine(
          Offset(-decoration.size.width / 2, 0),
          Offset(decoration.size.width / 2, 0),
          paint,
        );
        break;
    }

    canvas.restore();
  }



  void _addAdvancedNoise(Canvas canvas, Size size, double level) {
    final random = math.Random();
    final paint = Paint()..color = Colors.white.withValues(alpha: 0.1 * level);

    for (int i = 0; i < (500 * level).toInt(); i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = random.nextDouble() * 2;
      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  void _applyDistortion(Canvas canvas, Size size, double level) {
    // 简化的扭曲效果实现
    canvas.save();
    canvas.translate(size.width / 2, size.height / 2);
    canvas.skew(level * 0.1, level * 0.05);
    canvas.translate(-size.width / 2, -size.height / 2);
    canvas.restore();
  }

  /// 添加文字发光效果
  void _addTextGlow(Canvas canvas, TextPainter textPainter, Size size) {
    final offset = Offset(
      (size.width - textPainter.width) / 2,
      (size.height - textPainter.height) / 2,
    );

    // 简单的发光效果实现
    textPainter.paint(canvas, offset);
  }

  /// 添加文字描边
  void _addTextOutline(
    Canvas canvas,
    TextPainter textPainter,
    Offset offset,
    ImageTemplate template,
  ) {
    // 简化的描边效果实现
    // 这里可以在未来添加更复杂的描边逻辑
  }

  /// 添加专业水印
  void _addProfessionalWatermark(
    Canvas canvas,
    Size size,
    TrafficImageConfig config,
    ImageTemplate template,
  ) {
    final style = TextStyle(
      fontSize: 12,
      color: Colors.white.withValues(alpha: 0.6),
      fontWeight: FontWeight.w300,
    );

    final textSpan = TextSpan(text: config.watermarkText, style: style);
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    // 在右下角添加水印
    final offset = Offset(
      size.width - textPainter.width - 20,
      size.height - textPainter.height - 20,
    );

    textPainter.paint(canvas, offset);
  }

  /// 后处理效果
  void _applyPostProcessing(Canvas canvas, Size size, ImageTemplate template) {
    // 添加整体色调调整、锐化等后处理效果
    // 这里可以实现更复杂的图像处理算法
  }

  List<double> _generateGradientStops(int colorCount) {
    if (colorCount <= 1) return [0.0];

    final stops = <double>[];
    for (int i = 0; i < colorCount; i++) {
      stops.add(i / (colorCount - 1));
    }
    return stops;
  }
}
